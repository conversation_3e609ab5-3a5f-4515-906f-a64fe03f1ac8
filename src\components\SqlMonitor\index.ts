/**
 * SqlMonitor 模块统一导出
 * 提供完整的SQL监控任务管理功能
 */

// 导出主要组件
export { default as AntdTable } from './components/AntdTable';

// 导出表单组件
export { BasicInfoForm, ComplexTaskForm, QuickSearchForm } from './components/forms';

// 导出弹窗组件
export {
  ModalManager,
  TaskFormModals,
  TaskFormModalsExtended,
  GroupManagementModal,
} from './components/modals';

// 导出抽屉组件
export { AlertDrawer } from './components/drawers';

// 导出标签页组件
export {
  AlertConfigTab,
  DatabaseConfigTab,
  NotificationConfigTab,
  OtherInfoConfigTab,
} from './components/tabs';

// 导出通用组件
export { TaskGroupSelect } from './components/common';

// 导出类型定义
export type * from './types';

// 导出常量
export * from './constants';

// 导出服务
export * from './services';

// 导出自定义hooks
export * from './hooks';

// 导出样式
export * from './styles';

// 默认导出主表格组件
export { default } from './components/AntdTable';
